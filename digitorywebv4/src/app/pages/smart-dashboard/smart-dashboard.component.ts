import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged, forkJoin } from 'rxjs';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';

// Third-party modules
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { ChartType, ChartData, ChartConfiguration } from 'chart.js';

// Services
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';
import { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';
import { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';

// Interfaces
interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type?: string;
}

interface DashboardMode {
  value: string;
  label: string;
  icon: string;
  disabled?: boolean;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatButtonToggleModule,
    MatTooltipModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];

  // Categories and subcategories data
  categories: any[] = [];
  subcategories: any[] = [];
  filteredCategories: any[] = [];
  filteredSubcategories: any[] = [];

  // Form controls
  readonly selectedLocationsCtrl = new FormControl<string[]>([]);
  readonly locationFilterCtrl = new FormControl('');
  readonly selectedCategoriesCtrl = new FormControl<string[]>([]);
  readonly categoryFilterCtrl = new FormControl('');
  readonly selectedSubcategoriesCtrl = new FormControl<string[]>([]);
  readonly subcategoryFilterCtrl = new FormControl('');
  readonly startDate = new FormControl();
  readonly endDate = new FormControl();
  readonly searchQuery = new FormControl('');
  readonly baseDateCtrl = new FormControl();
  readonly dashboardModeCtrl = new FormControl('default');
  selectedDashboard = '';

  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartModel[] = [];
  isLoading = true;
  isConfigLoaded = false;

  // Dynamic configuration data
  dashboardTypes: DashboardType[] = [];
  baseDateOptions: BaseDateOption[] = [];

  // Dashboard modes
  readonly dashboardModes: DashboardMode[] = [
    { value: 'default', label: 'Default', icon: 'dashboard' },
    { value: 'ask_digi_ai', label: 'Ask Digi AI', icon: 'smart_toy', disabled: true }
  ];





  constructor(
    private readonly smartDashboardService: SmartDashboardService,
    private readonly authService: AuthService,
    private readonly shareDataService: ShareDataService,
    private readonly configService: DashboardConfigService,
    private readonly chartRenderer: ChartRendererService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {
    this.user = this.authService.getCurrentUser();
  }

  private initializeConfig(): void {
    // Load dashboard configuration on component initialization
    this.configService.loadConfig().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.configService.setConfig(response.data);
          this.setupDynamicConfigurations(response.data);
        } else {
          this.setupDefaultConfigurations();
        }
        this.isConfigLoaded = true;
        this.cdr.detectChanges();
      },
      error: () => {
        this.setupDefaultConfigurations();
        this.isConfigLoaded = true;
        this.cdr.detectChanges();
      }
    });
  }

  private setupDynamicConfigurations(config: any): void {
    // Set dashboard types - reorder to put inventory first
    const originalTypes = config.dashboard_types || [];
    this.dashboardTypes = originalTypes.sort((a: any, b: any) => {
      if (a.value === 'inventory') return -1;
      if (b.value === 'inventory') return 1;
      return 0;
    });

    // Set base date options
    this.baseDateOptions = config.base_date_options || [];

    // Check URL parameters first, then fallback to defaults
    const urlDashboard = this.route.snapshot.queryParams['dashboard'];

    // Set default form values - inventory first priority
    this.selectedDashboard = urlDashboard || 'inventory';
    this.baseDateCtrl.setValue('deliveryDate');

    // If no dashboard in URL, set inventory as default in URL
    if (!urlDashboard) {
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { dashboard: 'inventory' },
        queryParamsHandling: 'merge'
      });
    }

    // Set date range based on dashboard type
    this.setDefaultDateRange();

    // Load dashboard data after configuration is set with default selections
    setTimeout(() => {
      this.loadDashboardData();
    }, 100);
  }

  private setupDefaultConfigurations(): void {
    // Fallback configurations if backend fails - inventory first
    this.dashboardTypes = [
      { value: 'inventory', label: 'Inventory Dashboard' },
      { value: 'purchase', label: 'Purchase Dashboard' },
    ];
    this.baseDateOptions = [
      { value: 'deliveryDate', label: 'Delivery Date' },
      { value: 'orderDate', label: 'Order Date' },
      { value: 'createdDate', label: 'Created Date' }
    ];

    // Check URL parameters first, then fallback to inventory
    const urlDashboard = this.route.snapshot.queryParams['dashboard'];
    this.selectedDashboard = urlDashboard || 'inventory';
    this.baseDateCtrl.setValue('deliveryDate');

    // If no dashboard in URL, set inventory as default in URL
    if (!urlDashboard) {
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { dashboard: 'inventory' },
        queryParamsHandling: 'merge'
      });
    }

    // Set date range based on dashboard type
    this.setDefaultDateRange();

    // Load dashboard data after fallback configuration is set with default selections
    setTimeout(() => {
      this.loadDashboardData();
    }, 100);
  }

  ngOnInit(): void {
    this.initializeConfig();
    this.initializeFilters();
    this.loadBranches();
    this.loadCategoriesAndSubcategories();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    // Location filter
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });

    // Category filter
    this.categoryFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterCategories(value || '');
      });

    // Subcategory filter
    this.subcategoryFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterSubcategories(value || '');
      });

    // Category selection changes - reload subcategories when categories are selected
    this.selectedCategoriesCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((selectedCategories: string[] | null) => {
        if (selectedCategories && selectedCategories.length > 0) {
          this.loadSubcategoriesForSelectedCategories();
        }
      });
  }

  private loadBranches(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];

        // Select all branches by default
        this.selectedLocationsCtrl.setValue(this.branches.map(branch => branch.restaurantIdOld));
      });
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private loadCategoriesAndSubcategories(): void {
    if (!this.user?.tenantId) {
      console.warn('No tenant ID available for loading categories and subcategories');
      return;
    }

    console.log('Loading categories and subcategories for tenant:', this.user.tenantId);

    // Load both categories and subcategories simultaneously
    forkJoin({
      categories: this.smartDashboardService.getCategories(this.user.tenantId),
      subcategories: this.smartDashboardService.getSubCategories(this.user.tenantId, [])
    }).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (responses) => {
        console.log('Combined response:', responses);

        // Process categories
        if (responses.categories && responses.categories.success && responses.categories.categories) {
          const categoryKeys = Object.keys(responses.categories.categories);
          this.categories = categoryKeys;
          this.filteredCategories = [...this.categories];
          console.log('Categories loaded:', this.categories);
        } else {
          console.warn('Categories response invalid:', responses.categories);
          this.categories = [];
          this.filteredCategories = [];
        }

        // Process subcategories
        if (responses.subcategories && responses.subcategories.success && responses.subcategories.subcategories) {
          const allSubcategories = [];
          Object.values(responses.subcategories.subcategories).forEach((categorySubcats: any) => {
            if (Array.isArray(categorySubcats)) {
              allSubcategories.push(...categorySubcats);
            }
          });
          this.subcategories = [...new Set(allSubcategories)];
          this.filteredSubcategories = [...this.subcategories];
          console.log('Subcategories loaded:', this.subcategories);
        } else {
          console.warn('Subcategories response invalid:', responses.subcategories);
          this.subcategories = [];
          this.filteredSubcategories = [];
        }

        // Trigger change detection
        this.cdr.detectChanges();

        // Load dashboard data after both categories and subcategories are loaded
        this.loadDashboardData();
      },
      error: (error) => {
        console.error('Error loading categories and subcategories:', error);
        this.categories = [];
        this.filteredCategories = [];
        this.subcategories = [];
        this.filteredSubcategories = [];
      }
    });
  }

  private loadSubcategoriesForSelectedCategories(): void {
    if (!this.user?.tenantId) {
      console.warn('No tenant ID available for loading subcategories');
      return;
    }

    // Get selected categories
    const selectedCategories = this.selectedCategoriesCtrl.value || [];
    console.log('Loading subcategories for selected categories:', selectedCategories);

    this.smartDashboardService.getSubCategories(this.user.tenantId, selectedCategories)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Filtered subcategories response:', response);
          if (response && response.success && response.subcategories) {
            // Handle the subcategories structure - flatten all subcategories from all categories
            const allSubcategories = [];
            Object.values(response.subcategories).forEach((categorySubcats: any) => {
              if (Array.isArray(categorySubcats)) {
                allSubcategories.push(...categorySubcats);
              }
            });
            this.subcategories = [...new Set(allSubcategories)]; // Remove duplicates
            this.filteredSubcategories = [...this.subcategories];
            console.log('Filtered subcategories loaded:', this.subcategories);

            // Trigger change detection
            this.cdr.detectChanges();
          } else {
            this.subcategories = [];
            this.filteredSubcategories = [];
          }
        },
        error: (error) => {
          console.error('Error loading filtered subcategories:', error);
          this.subcategories = [];
          this.filteredSubcategories = [];
        }
      });
  }

  private filterCategories(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredCategories = [...this.categories];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredCategories = this.categories.filter(category =>
        category.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  private filterSubcategories(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredSubcategories = [...this.subcategories];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredSubcategories = this.subcategories.filter(subcategory =>
        subcategory.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  loadDashboardData(): void {
    this.loadDashboardDataInternal();
  }

  private clearDashboardData(): void {
    this.summaryCards = [];
    this.charts = [];
  }

  private processDashboardData(data: any): void {
    // Process summary cards using smart dashboard service
    this.summaryCards = data.summary_items?.map((item: any) => ({
      icon: item.icon || this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),
      value: item.value,
      label: item.label,
      color: this.configService.getContextualColor(0, item.data_type),
      data_type: item.data_type
    })) || [];

    // Process charts using chart renderer service
    this.charts = data.charts?.map((chart: any) =>
      this.chartRenderer.processChart(chart)
    ) || [];
  }

  private formatDate(date: Date): string {
    // Fix the date offset issue by using local date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private setDefaultDateRange(): void {
    const today = new Date();

    // For both dashboards: current month start to current date
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    this.startDate.setValue(startOfMonth);
    this.endDate.setValue(today);
  }

  onLocationChange(): void {
    // No automatic API call - user must click Search button
  }

  onDateChange(): void {
    // No automatic API call - user must click Search button
  }

  onDashboardChange(): void {
    // Update URL parameter when dashboard changes
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { dashboard: this.selectedDashboard },
      queryParamsHandling: 'merge'
    });

    // Update date range when dashboard type changes
    this.setDefaultDateRange();

    // Reload categories and subcategories for the new dashboard type
    this.loadCategoriesAndSubcategories();
  }

  searchDashboard(): void {
    // This method is called when Search button is clicked
    this.loadDashboardData();
  }

  resetFilters(): void {
    // Reset all filters to default values
    this.selectedLocationsCtrl.setValue(this.branches.map(branch => branch.restaurantIdOld));
    this.selectedCategoriesCtrl.setValue([]);
    this.selectedSubcategoriesCtrl.setValue([]);
    this.setDefaultDateRange();
    this.baseDateCtrl.setValue('deliveryDate');
    this.searchQuery.setValue('');
    this.locationFilterCtrl.setValue('');
    this.categoryFilterCtrl.setValue('');
    this.subcategoryFilterCtrl.setValue('');
    this.filteredBranches = [...this.branches];
    this.filteredCategories = [...this.categories];
    this.filteredSubcategories = [...this.subcategories];

    // Load dashboard data with reset filters
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    this.loadDashboardDataInternal(query, false);
  }

  private loadDashboardDataInternal(userQuery: string = '', useDefaultCharts: boolean = true): void {
    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      this.isLoading = false;
      this.clearDashboardData();
      this.cdr.detectChanges();
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocationsCtrl.value || [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate',
      categories: this.selectedCategoriesCtrl.value || [],
      subcategories: this.selectedSubcategoriesCtrl.value || []
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: userQuery,
      use_default_charts: useDefaultCharts,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Dynamic chart methods using services
  getChartData(chart: ChartModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartModel): ChartType {
    return this.chartRenderer.getChartType(chart.type);
  }

  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {
    return chart.options || this.configService.getDefaultChartOptions();
  }

  getChartCssClass(chart: ChartModel): string {
    return this.chartRenderer.getChartCssClass(chart);
  }

  // Dashboard mode methods
  isSearchBarDisabled(): boolean {
    return this.dashboardModeCtrl.value === 'default' || this.isAiModeDisabled();
  }

  isAiModeDisabled(): boolean {
    return this.dashboardModes.find(mode => mode.value === 'ask_digi_ai')?.disabled || false;
  }

  setDashboardMode(mode: string): void {
    if (mode === 'ask_digi_ai' && this.isAiModeDisabled()) {
      // Don't allow switching to AI mode if it's disabled
      return;
    }
    this.dashboardModeCtrl.setValue(mode);
  }
}
